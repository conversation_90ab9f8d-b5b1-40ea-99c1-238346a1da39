#!/bin/bash

# Rete Studio Docker 开发环境启动脚本

set -e

echo "🚀 启动 Rete Studio 开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 构建并启动开发环境
echo "📦 构建开发环境镜像..."
docker-compose --profile dev build rete-studio-dev

echo "🔧 启动开发环境..."
docker-compose --profile dev up rete-studio-dev

echo "✅ 开发环境已启动！"
echo "🌐 访问地址: http://localhost:5173"
