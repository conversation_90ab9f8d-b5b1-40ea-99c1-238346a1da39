#!/bin/bash

# Rete Studio Docker 清理脚本

set -e

echo "🧹 清理 Rete Studio Docker 环境..."

# 停止所有相关容器
echo "🛑 停止所有容器..."
docker-compose --profile dev --profile prod down

# 清理镜像
echo "🗑️ 清理镜像..."
docker images | grep rete-studio | awk '{print $3}' | xargs -r docker rmi -f

# 清理缓存卷（可选）
read -p "是否清理缓存卷？这将删除所有npm缓存 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️ 清理缓存卷..."
    docker-compose down -v
    docker volume prune -f
fi

# 清理构建缓存
echo "🗑️ 清理Docker构建缓存..."
docker builder prune -f

echo "✅ 清理完成！"
