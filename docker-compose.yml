services:
  # 开发环境服务
  rete-studio-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5173:5173"
    volumes:
      # 挂载源代码目录用于开发
      - .:/app
      # 缓存npm依赖
      - npm_cache:/root/.npm
      # 缓存node_modules
      - node_modules_core:/app/core/node_modules
      - node_modules_ui:/app/ui/node_modules
      - node_modules_demo:/app/demo/node_modules
      - node_modules_template:/app/languages/template/node_modules
      - node_modules_javascript:/app/languages/javascript/node_modules
    environment:
      - NODE_ENV=development
      - npm_config_cache=/root/.npm
    command: sh -c "cd demo && npm install --registry=https://registry.npmmirror.com && npm run dev"
    restart: unless-stopped
    stdin_open: true
    tty: true
    profiles:
      - dev

  # 生产环境服务
  rete-studio-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
    volumes:
      # 缓存npm依赖（仅构建时使用）
      - npm_cache:/root/.npm
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    profiles:
      - prod

volumes:
  # npm缓存卷
  npm_cache:
    driver: local
  # node_modules缓存卷
  node_modules_core:
    driver: local
  node_modules_ui:
    driver: local
  node_modules_demo:
    driver: local
  node_modules_template:
    driver: local
  node_modules_javascript:
    driver: local
