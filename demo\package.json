{"name": "rete-studio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.1.4", "@babel/core": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/parser": "^7.20.7", "@babel/traverse": "^7.20.10", "@monaco-editor/react": "^4.5.1", "antd": "^5.6.1", "elkjs": "^0.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "rete": "^2.0.3", "rete-area-plugin": "^2.0.2", "rete-auto-arrange-plugin": "^2.0.1", "rete-connection-plugin": "^2.0.1", "rete-connection-reroute-plugin": "^2.0.1", "rete-context-menu-plugin": "^2.0.1", "rete-history-plugin": "^2.0.1", "rete-react-plugin": "^2.0.5", "rete-render-utils": "^2.0.2", "rete-scopes-plugin": "^2.0.1", "rete-structures": "^2.0.1", "rete-studio-core": "file:../core/rete-studio-core-0.0.0.tgz", "rete-studio-javascript-lang": "file:../languages/javascript/rete-studio-javascript-lang-0.0.0.tgz", "rete-studio-template-lang": "file:../languages/template/rete-studio-template-lang-0.0.0.tgz", "rete-studio-ui": "file:../ui/rete-studio-ui-0.0.0.tgz", "styled-components": "^6.0.0", "usehooks-ts": "^2.9.1", "web-worker": "^1.2.0", "worker-bridge": "^0.1.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^5.2.11", "vite-plugin-node-polyfills": "^0.21.0"}}