@echo off
echo 🚀 启动 Rete Studio 开发环境...

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未运行，请先启动 Docker
    pause
    exit /b 1
)

REM 检查docker-compose是否可用
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose 未安装
    pause
    exit /b 1
)

REM 构建并启动开发环境
echo 📦 构建开发环境镜像...
docker-compose --profile dev build rete-studio-dev

echo 🔧 启动开发环境...
docker-compose --profile dev up rete-studio-dev

echo ✅ 开发环境已启动！
echo 🌐 访问地址: http://localhost:5173
pause
