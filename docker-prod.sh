#!/bin/bash

# Rete Studio Docker 生产环境启动脚本

set -e

echo "🚀 启动 Rete Studio 生产环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查docker-compose是否可用
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose 未安装"
    exit 1
fi

# 构建并启动生产环境
echo "📦 构建生产环境镜像..."
docker-compose --profile prod build rete-studio-prod

echo "🔧 启动生产环境..."
docker-compose --profile prod up -d rete-studio-prod

echo "✅ 生产环境已启动！"
echo "🌐 访问地址: http://localhost"
echo "📊 查看日志: docker-compose --profile prod logs -f rete-studio-prod"
echo "🛑 停止服务: docker-compose --profile prod down"
