# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    git \
    bash \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/* \
    && npm config set registry https://registry.npmmirror.com

# 创建npm缓存目录
RUN mkdir -p /root/.npm

# 复制package.json文件
COPY core/package*.json ./core/
COPY ui/package*.json ./ui/
COPY demo/package*.json ./demo/
COPY languages/template/package*.json ./languages/template/
COPY languages/javascript/package*.json ./languages/javascript/

# 预安装依赖
RUN cd core && npm ci --cache /root/.npm --only=production
RUN cd ui && npm ci --cache /root/.npm --only=production
RUN cd demo && npm ci --cache /root/.npm
RUN cd languages/template && npm ci --cache /root/.npm --only=production
RUN cd languages/javascript && npm ci --cache /root/.npm --only=production

# 复制源代码
COPY . .

# 构建项目
ENV NODE_ENV=production
RUN chmod +x ./setup.sh && bash ./setup.sh
RUN cd demo && npm run build

# 生产阶段 - 使用nginx提供静态文件
FROM nginx:alpine AS production

# 复制构建结果
COPY --from=builder /app/demo/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
