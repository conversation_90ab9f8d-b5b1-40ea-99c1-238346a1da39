# 使用官方Node.js镜像作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖并清理缓存
RUN apk add --no-cache \
    git \
    bash \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/* \
    && npm config set cache /root/.npm \
    && npm config set registry https://registry.npmmirror.com

# 创建缓存目录
RUN mkdir -p /root/.npm

# 复制源代码
COPY . .

# 设置环境变量
ENV npm_config_cache=/root/.npm
ENV NODE_ENV=development

# 运行setup脚本来构建所有包（这会安装依赖并构建包）
# 先转换换行符，然后运行脚本
RUN sed -i 's/\r$//' ./setup.sh && chmod +x ./setup.sh && \
    # 修改setup脚本以跳过构建失败的包
    sed -i 's/npm run build/npm run build || echo "Build failed, continuing..."/g' ./setup.sh && \
    bash ./setup.sh || echo "Setup completed with some failures" && \
    # 安装demo目录的依赖
    cd demo && npm install --registry=https://registry.npmmirror.com

# 切换到demo目录
WORKDIR /app/demo

# 暴露端口
EXPOSE 5173

# 启动命令
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
