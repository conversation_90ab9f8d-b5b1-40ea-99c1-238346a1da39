{"name": "rete-studio-ui", "version": "0.0.0", "description": "", "scripts": {"build": "rete build -c rete.config.ts", "lint": "rete lint"}, "author": "<PERSON><PERSON><PERSON>", "license": "CC-BY-NC-SA-4.0", "dependencies": {"@ant-design/icons": "^5.2.0", "@monaco-editor/react": "^4.5.0", "antd": "^5.0.0", "elkjs": ">0.8.0", "react": "^18.0.0", "react-dom": "^18.0.0", "rete-auto-arrange-plugin": "^2.0.1", "rete-connection-plugin": "^2.0.2", "rete-context-menu-plugin": "^2.0.3", "rete-history-plugin": "^2.0.1", "rete-react-plugin": "^2.0.5", "rete-scopes-plugin": "^2.0.1", "rete-structures": "^2.0.1"}, "peerDependencies": {"styled-components": "^6.0.0", "rete-studio-core": "file:../core/rete-studio-core-0.0.0.tgz"}, "devDependencies": {"@types/styled-components": "^5.1.26", "@types/react-dom": "^18.2.7", "@babel/preset-react": "^7.22.15", "eslint-plugin-react": "^7.33.2", "@rollup/plugin-commonjs": "^25.0.4", "styled-components": "^6.0.7", "rollup-plugin-copy": "^3.5.0", "rete-cli": "^1.0.3", "rete-studio-core": "file:../core/rete-studio-core-0.0.0.tgz"}, "main": "dist/rete-studio-u-i.common.js", "module": "dist/rete-studio-u-i.esm.js", "types": "dist/_types/index.d.ts"}