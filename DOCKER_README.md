# Rete Studio Docker 部署指南

本指南介绍如何使用 Docker 部署 Rete Studio 应用，包括开发环境和生产环境的配置。

## 🚀 快速开始

### 前置要求

- Docker (版本 20.10+)
- Docker Compose (版本 2.0+)

### 开发环境

#### Linux/macOS
```bash
# 启动开发环境
./docker-dev.sh
```

#### Windows
```cmd
# 启动开发环境
docker-dev.bat
```

#### 手动启动
```bash
# 构建并启动开发环境
docker-compose --profile dev up --build rete-studio-dev
```

开发环境将在 http://localhost:5173 启动，支持热重载。

### 生产环境

#### Linux/macOS
```bash
# 启动生产环境
./docker-prod.sh
```

#### Windows
```cmd
# 启动生产环境
docker-prod.bat
```

#### 手动启动
```bash
# 构建并启动生产环境
docker-compose --profile prod up -d --build rete-studio-prod
```

生产环境将在 http://localhost 启动，使用 Nginx 提供静态文件服务。

## 📁 文件说明

- `Dockerfile` - 开发环境镜像配置
- `Dockerfile.prod` - 生产环境镜像配置
- `docker-compose.yml` - Docker Compose 配置文件
- `nginx.conf` - Nginx 配置文件（生产环境）
- `docker-dev.sh/.bat` - 开发环境启动脚本
- `docker-prod.sh/.bat` - 生产环境启动脚本
- `docker-clean.sh` - 清理脚本

## 🔧 缓存优化

本配置包含以下缓存优化：

1. **npm 缓存**: 使用 Docker 卷持久化 npm 缓存
2. **node_modules 缓存**: 每个包的 node_modules 都有独立的卷
3. **Docker 层缓存**: 优化 Dockerfile 层顺序，最大化缓存利用
4. **npm 镜像**: 使用国内镜像源加速下载

## 📊 常用命令

```bash
# 查看运行状态
docker-compose ps

# 查看开发环境日志
docker-compose --profile dev logs -f rete-studio-dev

# 查看生产环境日志
docker-compose --profile prod logs -f rete-studio-prod

# 停止开发环境
docker-compose --profile dev down

# 停止生产环境
docker-compose --profile prod down

# 重新构建镜像
docker-compose --profile dev build --no-cache rete-studio-dev

# 清理所有资源
./docker-clean.sh
```

## 🐛 故障排除

### 端口冲突
如果端口被占用，可以修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "3000:5173"  # 将本地端口改为 3000
```

### 权限问题
在 Linux/macOS 上，确保脚本有执行权限：
```bash
chmod +x docker-dev.sh docker-prod.sh docker-clean.sh
```

### 缓存问题
如果遇到依赖问题，可以清理缓存：
```bash
# 清理 Docker 缓存
docker builder prune -f

# 清理卷缓存
docker-compose down -v
```

## 🔄 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose --profile dev up --build rete-studio-dev
```

## 📝 自定义配置

### 环境变量
可以创建 `.env` 文件来自定义配置：
```env
NODE_ENV=development
npm_config_cache=/root/.npm
```

### 开发模式挂载
开发环境默认挂载源代码目录，支持实时编辑。如不需要，可以注释掉 `docker-compose.yml` 中的卷挂载。
